{"app": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Grow your Twitch community"}, "auth": {"welcome": "Welcome to TwGrow", "login_with_twitch": "Login with Twitch", "connect_twitch": "Connect Twitch Account", "terms_agreement": "By continuing, you agree to our", "terms_of_service": "Terms of Service", "and": "and", "privacy_policy": "Privacy Policy", "login_failed": "<PERSON><PERSON> failed. Please try again.", "connect_failed": "Failed to connect. Please try again."}, "loading": {"setting_up_account": "Setting up your account...", "retry": "Retry", "connection_failed": "Failed to connect. Please try again."}, "dashboard": {"channel": "Channel", "campaign": "Campaign", "promote": "Promote", "buy": "Buy", "buy_coins": "Buy Coins", "logout": "Logout", "rate_app": "Rate App", "privacy_policy": "Privacy Policy", "eula": "EULA", "thanks_feedback": "Thanks for your feedback!", "grow_your_audience": "Grow Your Audience"}, "campaigns": {"no_active_campaigns": "No Active Campaigns", "start_campaign": "Start a new campaign to grow your audience", "create_campaign": "Create Campaign", "delete_campaign": "Delete Campaign", "cannot_undo": "This action cannot be undone", "no_refund": "Coins spent on this campaign will not be refunded", "cancel": "Cancel", "delete": "Delete", "your_account": "YOUR ACCOUNT"}, "packages": {"starter": {"name": "Starter", "tag": "BASIC"}, "growth": {"name": "Growth", "tag": "POPULAR"}, "accelerate": {"name": "Accelerate", "tag": "VALUE"}, "pro": {"name": "Pro", "tag": "PRO"}, "ultimate": {"name": "Ultimate", "tag": "BEST VALUE"}}, "promote": {"search_profile": "Search Twitch Profile", "enter_username": "Enter username", "confirm_campaign": "Confirm Campaign", "target_followers": "Target Followers", "cost": "Cost", "coins_amount": "{} coins", "creating": "Creating...", "campaign_created": "Campaign Created!", "campaign_success": "Your {} promotion campaign has been created successfully.", "need_more_coins": "You need {} more coins for this promotion", "active": "Active", "promote": "Promote", "buy_coins": "Buy Coins", "cancel": "Cancel", "your_account": "YOUR ACCOUNT", "search_prompt": "Search for Twitch profiles", "no_profiles": "No profiles found", "search_error": "Error searching users: {}", "creation_failed": "Failed to create campaign. Please try again.", "followers_count": "{} Followers", "done": "Done", "promote_your_channel": "Promote Your Channel", "search_description": "Search for a channel to start promoting and grow your audience", "search_channels": "Search Channels", "auto_save": "Auto Save", "selection_remembered": "Selection remembered", "easy_switch": "Easy Switch", "change_anytime": "Change anytime", "grow_fast": "<PERSON>row Fast", "boost_followers": "Boost followers"}, "buy": {"buying": "Buying...", "purchase_success": "Successfully purchased {amount} coins!", "purchase_failed": "Purchase failed: {error}"}, "follow": {"following": "Following...", "adding_coins": "Adding coins...", "follow": "Follow", "see_other": "See Other", "verify_failed": "Failed to verify follow status. Please try again.", "launch_failed": "Failed to open Twitch. Please try again.", "coins_earned": "You earned {} coins!", "connect_with_twitch": "Connect with Twitch", "login_description": "Login with your Twitch account to follow streamers and start earning coins!", "login_with_twitch": "Login with Twitch", "maybe_later": "Maybe Later", "coins_reward": "+15 coins"}, "twitch_login": {"load_error": "Failed to load authentication page. Please try again.", "browser_error": "Browser compatibility issue detected. Please try again or contact support.", "retry": "Retry", "authenticating": "Authenticating...", "connecting": "Connecting to Twitch..."}, "rate": {"experience": "How was your experience with us?", "maybe_later": "Maybe Later", "value_feedback": "We value your feedback", "improve_message": "Please let us know how we can improve", "your_feedback": "Your feedback...", "send_feedback": "Send Feedback", "thanks_rating": "Thanks for your positive rating!", "thanks_feedback": "Thank you for your valuable feedback!", "store_error": "Could not open store page", "cancel": "Cancel"}, "welcome_bonus": {"title": "Welcome Bonus!", "description": "Get {} coins as a welcome gift to start growing your Twitch community!", "claim_button": "Claim {} Coins", "claimed_success": "Congratulations! You've received {} coins!", "claim_failed": "Failed to claim bonus. Please try again."}}